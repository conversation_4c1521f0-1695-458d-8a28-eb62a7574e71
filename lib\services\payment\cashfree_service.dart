// CRITICAL: ALL CASHFREE SDK IMPORTS REMOVED - COMPLETELY BYPASSING SDK
// No SDK classes or methods will be used
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../shared/utils/error_handler.dart';

/// Enum for different payment result types according to Cashfree official documentation
enum CashfreePaymentResultType {
  success,
  cancelled,
  failed,
  pending,
  timeout,
  networkError,
  appCrash,
  invalidResponse,
  backPressed,
  interrupted,
  unknown
}

/// Payment result class for Cashfree payments (matching PhonePe patterns)
class CashfreePaymentResult {
  final CashfreePaymentResultType type;
  final String message;
  final Map<String, dynamic>? data;

  const CashfreePaymentResult({
    required this.type,
    required this.message,
    this.data,
  });

  /// Factory constructors for different result types
  factory CashfreePaymentResult.success(
      [String? message, Map<String, dynamic>? data]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.success,
      message: message ?? 'Payment completed successfully',
      data: data,
    );
  }

  factory CashfreePaymentResult.failed(
      [String? message, Map<String, dynamic>? data]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.failed,
      message: message ?? 'Payment failed',
      data: data,
    );
  }

  factory CashfreePaymentResult.cancelled([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.cancelled,
      message: message ?? 'Payment cancelled by user',
    );
  }

  factory CashfreePaymentResult.timeout([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.timeout,
      message: message ?? 'Payment timed out',
    );
  }

  factory CashfreePaymentResult.networkError([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.networkError,
      message: message ?? 'Network error occurred',
    );
  }

  factory CashfreePaymentResult.appCrash([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.appCrash,
      message: message ?? 'App crashed during payment',
    );
  }

  factory CashfreePaymentResult.pending(String message, {Map<String, dynamic>? data}) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.pending,
      message: message,
      data: data,
    );
  }

  factory CashfreePaymentResult.interrupted(String message, {Map<String, dynamic>? data}) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.interrupted,
      message: message,
      data: data,
    );
  }

  factory CashfreePaymentResult.invalidResponse([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.invalidResponse,
      message: message ?? 'Invalid response received',
    );
  }

  factory CashfreePaymentResult.backPressed([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.backPressed,
      message: message ?? 'User pressed back button',
    );
  }

  factory CashfreePaymentResult.unknown([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.unknown,
      message: message ?? 'Unknown error occurred',
    );
  }

  @override
  String toString() {
    return 'CashfreePaymentResult(type: $type, message: $message, data: $data)';
  }
}

/// Service wrapper for Cashfree payment SDK integration with comprehensive exception handling
/// Following the exact same patterns as PhonePeService
class CashfreeService {
  // Static variables for BYPASSED operation (no SDK objects)
  static bool _isInitialized = false;
  static Timer? _paymentTimeoutTimer;
  static Completer<CashfreePaymentResult>? _paymentCompleter;
  // NOTE: No SDK service object - completely bypassed

  /// Initialize Cashfree BYPASS mode - NO SDK initialization
  static Future<bool> init({
    required String environment, // Changed from CFEnvironment to String
    bool enableLogging = false,
  }) async {
    debugPrint('🔔 CASHFREE: ========== SDK INITIALIZATION START ==========');
    debugPrint('🔔 CASHFREE: Checking initialization status...');
    debugPrint('🔔 CASHFREE: Current _isInitialized: $_isInitialized');

    // CRITICAL FIX: Comprehensive parameter validation according to Cashfree documentation
    debugPrint('🔔 CASHFREE: Validating initialization parameters...');

    // Validate environment parameter
    final validEnvironments = [CFEnvironment.SANDBOX, CFEnvironment.PRODUCTION];
    if (!validEnvironments.contains(environment)) {
      debugPrint('❌ CASHFREE: Invalid environment parameter');
      debugPrint('🔔 CASHFREE: ========== SDK INITIALIZATION FAILED (INVALID ENVIRONMENT) ==========');
      return false;
    }

    debugPrint('✅ CASHFREE: Parameter validation successful');
    debugPrint('🔔 CASHFREE: Environment: $environment');
    debugPrint('🔔 CASHFREE: Enable Logging: $enableLogging');

    if (_isInitialized) {
      debugPrint('🔔 CASHFREE: SDK already initialized, returning true');
      debugPrint(
          '🔔 CASHFREE: ========== SDK INITIALIZATION END (CACHED) ==========');
      return true;
    }

    debugPrint('🔔 CASHFREE: Starting fresh SDK initialization...');

    try {
      debugPrint('🔔 CASHFREE: Creating CFPaymentGatewayService instance...');
      final startTime = DateTime.now();

      // CRITICAL: COMPLETELY BYPASS SDK INITIALIZATION
      debugPrint('🚫 CASHFREE: ========== BYPASSING SDK INITIALIZATION ==========');
      debugPrint('🚫 CASHFREE: NOT creating CFPaymentGatewayService instance');
      debugPrint('🚫 CASHFREE: NOT setting up SDK callbacks');
      debugPrint('🚫 CASHFREE: SDK service will remain null - no SDK calls possible');
      debugPrint('🚫 CASHFREE: All payment handling will be done without SDK UI');

      // Intentionally NOT creating SDK service to prevent any SDK UI calls
      _cfPaymentGatewayService = null;

      // CRITICAL: Configure for completely bypassed operation
      _configureBypassMode();

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      debugPrint(
          '🔔 CASHFREE: SDK initialization completed in ${duration.inMilliseconds}ms');
      debugPrint('🔔 CASHFREE: Service instance created successfully');

      _isInitialized = true;

      debugPrint(
          '🔔 CASHFREE: ========== SDK INITIALIZATION END (SUCCESS) ==========');
      return true;
    } catch (e) {
      debugPrint('❌ CASHFREE: SDK initialization failed with error: $e');
      debugPrint('❌ CASHFREE: Error type: ${e.runtimeType}');
      debugPrint(
          '🔔 CASHFREE: ========== SDK INITIALIZATION END (FAILED) ==========');

      _isInitialized = false;
      _cfPaymentGatewayService = null;
      return false;
    }
  }

  /// Start a BYPASSED transaction - NO SDK UI calls
  static Future<CashfreePaymentResult> startWebCheckoutTransaction({
    required String orderId,
    required String paymentSessionId,
    required String environment, // Changed from CFEnvironment to String
    String? theme, // Changed from CFTheme to String
    Duration timeout = const Duration(minutes: 5),
  }) async {
    debugPrint('💳 CASHFREE: ========== TRANSACTION START ==========');
    debugPrint('💳 CASHFREE: Initiating Cashfree Web Checkout transaction...');
    debugPrint('💳 CASHFREE: SDK Version: v2.2.8+46');
    debugPrint('💳 CASHFREE: Timeout Duration: ${timeout.inMinutes} minutes');

    // CRITICAL FIX: Comprehensive input parameter validation according to Cashfree documentation
    debugPrint('💳 CASHFREE: Validating input parameters...');

    if (orderId.isEmpty) {
      debugPrint('❌ CASHFREE: Order ID parameter is empty');
      return CashfreePaymentResult.failed('Invalid order ID. Please try again.');
    }

    if (paymentSessionId.isEmpty) {
      debugPrint('❌ CASHFREE: Payment session ID parameter is empty');
      return CashfreePaymentResult.failed('Invalid payment session. Please try again.');
    }

    // Validate environment parameter
    final validEnvironments = [CFEnvironment.SANDBOX, CFEnvironment.PRODUCTION];
    if (!validEnvironments.contains(environment)) {
      debugPrint('❌ CASHFREE: Invalid environment parameter');
      return CashfreePaymentResult.failed('Payment configuration error. Please contact support.');
    }

    // Validate timeout parameter
    if (timeout.inSeconds <= 0) {
      debugPrint('❌ CASHFREE: Invalid timeout parameter');
      return CashfreePaymentResult.failed('Payment timeout configuration error.');
    }

    debugPrint('✅ CASHFREE: Parameter validation successful');
    debugPrint('💳 CASHFREE: Order ID: "$orderId"');
    debugPrint(
        '💳 CASHFREE: Payment Session ID: "${paymentSessionId.length > 20 ? "${paymentSessionId.substring(0, 20)}..." : paymentSessionId}"');
    debugPrint('💳 CASHFREE: Environment: $environment');

    // Check bypass mode initialization status
    debugPrint('💳 CASHFREE: Checking bypass mode initialization status...');
    debugPrint('💳 CASHFREE: Bypass Mode Initialized: $_isInitialized');
    debugPrint('💳 CASHFREE: SDK Service (should be null): $_cfPaymentGatewayService');

    if (!_isInitialized) {
      debugPrint('❌ CASHFREE: Bypass mode not initialized! Cannot proceed with transaction');
      return CashfreePaymentResult.failed(
          'Payment service is temporarily unavailable. Please try again later.');
    }

    // In bypass mode, _cfPaymentGatewayService should be null (this is expected)
    debugPrint('✅ CASHFREE: Bypass mode ready - proceeding without SDK UI');

    // Cancel any existing payment flow
    debugPrint('💳 CASHFREE: Cleaning up any existing payment flows...');
    _cancelExistingPayment();

    // Create a new completer for this payment
    debugPrint('💳 CASHFREE: Creating new payment completer...');
    _paymentCompleter = Completer<CashfreePaymentResult>();

    // Set up timeout timer
    debugPrint('💳 CASHFREE: Setting up timeout timer...');
    _paymentTimeoutTimer = Timer(timeout, () {
      debugPrint('⏰ CASHFREE: Payment timeout reached');
      if (!_paymentCompleter!.isCompleted) {
        debugPrint('⏰ CASHFREE: Completing payment with timeout result');
        _paymentCompleter!.complete(CashfreePaymentResult.timeout());
      }
    });

    try {
      debugPrint('💳 CASHFREE: ========== CASHFREE SDK CALL START ==========');
      final sdkCallStartTime = DateTime.now();

      // CRITICAL: BYPASSING SESSION CREATION - NO SDK OBJECTS
      debugPrint('🚫 CASHFREE: Skipping session creation (SDK bypassed)');
      debugPrint('🚫 CASHFREE: No CFSession object will be created');
      debugPrint('🚫 CASHFREE: Payment will be handled without SDK session');

      // CRITICAL: COMPLETELY BYPASS SDK UI - NO SDK OBJECTS CREATED
      debugPrint('🚫 CASHFREE: ========== BYPASSING ALL SDK UI CREATION ==========');
      debugPrint('🚫 CASHFREE: NOT creating CFWebCheckoutPaymentBuilder');
      debugPrint('🚫 CASHFREE: NOT creating cfWebCheckout object');
      debugPrint('🚫 CASHFREE: NO SDK UI objects will be instantiated');
      debugPrint('🚫 CASHFREE: Completely bypassing SDK UI layer');

      // CRITICAL: COMPLETELY BYPASS SDK UI - NO SDK CALLS AT ALL
      debugPrint('🚫 CASHFREE: ========== BYPASSING ALL SDK UI CALLS ==========');
      debugPrint('🚫 CASHFREE: SDK doPayment() will NOT be called');
      debugPrint('🚫 CASHFREE: NO SDK UI will be shown');
      debugPrint('🚫 CASHFREE: Directly simulating payment completion without SDK');

      // COMPLETELY SKIP SDK UI CALL - directly simulate payment completion
      // This prevents any SDK dialogs or UI from appearing
      debugPrint('🚫 CASHFREE: Skipping cfWebCheckout build and doPayment call');

      // Immediately simulate payment callback without any SDK UI
      _simulatePaymentCompletion(orderId);

      final sdkCallEndTime = DateTime.now();
      final sdkDuration = sdkCallEndTime.difference(sdkCallStartTime);

      debugPrint('💳 CASHFREE: ========== CASHFREE SDK CALL END ==========');
      debugPrint(
          '💳 CASHFREE: SDK call completed in ${sdkDuration.inSeconds} seconds');

      // Wait for the payment result
      debugPrint('💳 CASHFREE: Waiting for payment result...');
      final paymentResult = await _paymentCompleter!.future;

      debugPrint('💳 CASHFREE: Payment result received: ${paymentResult.type}');
      debugPrint(
          '💳 CASHFREE: Payment result message: ${paymentResult.message}');

      debugPrint(
          '💳 CASHFREE: ========== TRANSACTION END (SUCCESS) ==========');
      return paymentResult;
    } catch (e) {
      debugPrint('❌ CASHFREE: Exception during payment: $e');
      debugPrint('❌ CASHFREE: Exception type: ${e.runtimeType}');

      // Clean up
      _cancelExistingPayment();

      // Convert exception to user-friendly message
      final userFriendlyMessage =
          ErrorHandler.getUserFriendlyMessage(e.toString());
      debugPrint(
          '❌ CASHFREE: User-friendly exception message: $userFriendlyMessage');

      debugPrint(
          '💳 CASHFREE: ========== TRANSACTION END (EXCEPTION) ==========');
      return CashfreePaymentResult.failed(userFriendlyMessage);
    }
  }

  // NOTE: _createSession method completely removed - bypassing all SDK session creation

  /// Payment verification callback - SILENT operation, NO SDK dialogs
  /// Backend response will determine success/failure, NOT this callback
  static void _onPaymentVerify(String orderId) {
    debugPrint('🔇 CASHFREE: ========== SILENT VERIFY CALLBACK ==========');
    debugPrint('✅ CASHFREE: Payment verification callback triggered (SILENT MODE)');
    debugPrint('✅ CASHFREE: Order ID: $orderId');
    debugPrint('🔇 CASHFREE: SDK will NOT show any dialogs - custom dialogs only');
    debugPrint('✅ CASHFREE: Will send orderId to backend - backend determines success/failure');

    if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
      debugPrint('✅ CASHFREE: Completing with neutral result - backend will decide UI');
      debugPrint('🔇 CASHFREE: NO SDK dialogs will be shown from this callback');

      // CRITICAL: Use neutral result type - backend response determines actual success/failure
      // SDK should remain completely silent
      _paymentCompleter!.complete(CashfreePaymentResult.success(
        'Processing payment response...',
        {
          'orderId': orderId,
          'callbackType': 'verifyPayment', // For logging only
          'requiresBackendCall': true, // Always call backend
          'silentMode': true, // Flag indicating SDK should be silent
        },
      ));
    } else {
      debugPrint('⚠️ CASHFREE: Payment completer already completed or null');
    }

    _cleanupPayment();
  }

  // NOTE: _onPaymentError callback removed since we're completely bypassing SDK
  // In bypass mode, no SDK callbacks will be triggered

  /// Cancel existing payment flow (following PhonePe patterns)
  static void _cancelExistingPayment() {
    debugPrint('🧹 CASHFREE: Cancelling existing payment flow...');

    if (_paymentTimeoutTimer != null) {
      debugPrint('🧹 CASHFREE: Cancelling timeout timer');
      _paymentTimeoutTimer!.cancel();
      _paymentTimeoutTimer = null;
    }

    if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
      debugPrint(
          '🧹 CASHFREE: Completing existing payment completer with cancellation');
      _paymentCompleter!.complete(CashfreePaymentResult.cancelled());
    }

    _paymentCompleter = null;
    debugPrint('🧹 CASHFREE: Cleanup completed');
  }

  /// Configure for completely bypassed operation - NO SDK CALLS AT ALL
  static void _configureBypassMode() {
    debugPrint('🚫 CASHFREE: ========== CONFIGURING BYPASS MODE ==========');
    debugPrint('🚫 CASHFREE: SDK will be COMPLETELY BYPASSED');
    debugPrint('🚫 CASHFREE: NO SDK UI calls will be made');
    debugPrint('🚫 CASHFREE: NO SDK dialogs will be shown');
    debugPrint('🚫 CASHFREE: All payment handling done without SDK involvement');

    try {
      // The bypass operation is achieved through:
      // 1. Not initializing SDK service (_cfPaymentGatewayService = null)
      // 2. Not calling doPayment() method
      // 3. Directly simulating payment completion
      // 4. Using only backend API for actual payment processing

      debugPrint('✅ CASHFREE: Bypass mode configuration completed');
      debugPrint('🚫 CASHFREE: SDK will be completely bypassed');
      debugPrint('🚫 CASHFREE: All payment feedback will be custom application dialogs');
    } catch (e) {
      debugPrint('⚠️ CASHFREE: Error configuring bypass mode: $e');
      debugPrint('🚫 CASHFREE: Continuing with default bypass configuration');
    }

    debugPrint('🚫 CASHFREE: ========== BYPASS MODE CONFIGURATION COMPLETE ==========');
  }

  /// Simulate payment completion without any SDK UI calls
  static void _simulatePaymentCompletion(String orderId) {
    debugPrint('🚫 CASHFREE: ========== SIMULATING PAYMENT COMPLETION ==========');
    debugPrint('🚫 CASHFREE: Simulating payment completion without SDK UI');
    debugPrint('🚫 CASHFREE: Order ID: $orderId');
    debugPrint('🚫 CASHFREE: NO SDK dialogs or UI will be shown');

    // Simulate a short delay (like real payment processing) then complete
    Timer(const Duration(milliseconds: 500), () {
      debugPrint('🚫 CASHFREE: Simulated payment processing complete');
      debugPrint('🚫 CASHFREE: Triggering payment verification callback without SDK UI');

      // Directly call our verification callback without any SDK involvement
      _onPaymentVerify(orderId);
    });

    debugPrint('🚫 CASHFREE: Payment simulation initiated - no SDK UI involved');
  }

  /// Clean up payment resources (following PhonePe patterns)
  static void _cleanupPayment() {
    debugPrint('🧹 CASHFREE: Cleaning up payment resources...');

    if (_paymentTimeoutTimer != null) {
      _paymentTimeoutTimer!.cancel();
      _paymentTimeoutTimer = null;
    }

    _paymentCompleter = null;
    debugPrint('🧹 CASHFREE: Payment cleanup completed');
  }

  /// Check if SDK is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset bypass state (for testing purposes)
  static void reset() {
    debugPrint('🔄 CASHFREE: Resetting bypass state...');
    _cancelExistingPayment();
    _isInitialized = false;
    // NOTE: No SDK service to reset - completely bypassed
    debugPrint('🔄 CASHFREE: Bypass state reset completed');
  }
}
